'use client';

import { useBoolean } from 'minimal-shared/hooks';
import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import TableBody from '@mui/material/TableBody';
import IconButton from '@mui/material/IconButton';
import TableContainer from '@mui/material/TableContainer';
import CircularProgress from '@mui/material/CircularProgress';

import { snakeToCamelObject } from 'src/utils/format-data/case-converter';

import { fetchData, createData, updateData } from 'src/actions/mooly-chatbot/supabase-utils';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import {
  useTable,
  emptyRows,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';

import InventoryTableToolbar from '../inventory-table-toolbar';
import InventoryTableFilters from '../inventory-table-filters';
import InventoryQuickEditForm from '../inventory-quick-edit-form';
import ProductInventoryTableRow from '../product-inventory-table-row';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'name', label: 'Sản phẩm', width: '35%' },
  { id: 'sku', label: 'SKU', width: '15%' },
  { id: 'type', label: 'Loại sản phẩm', width: '15%' },
  { id: 'quantity', label: 'Số lượng', align: 'center', width: '15%' },
  { id: 'status', label: 'Trạng thái', width: '10%' },
  // { id: '', width: '10%' },
];

// ----------------------------------------------------------------------

ProductInventoryView.propTypes = {};

export default function ProductInventoryView() {
  const table = useTable();
  const quickEdit = useBoolean();

  const [tableData, setTableData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    name: '',
    status: [],
    type: [],
  });
  // Hàm để tải dữ liệu sản phẩm kèm tồn kho - tối ưu với một truy vấn duy nhất
  const fetchProductsInventory = useCallback(async () => {
    try {
      setIsLoading(true);

      // Sử dụng fetchData từ supabase-utils để lấy dữ liệu
      const { success, data: products, error: productsError } = await fetchData('products', {
        columns: `
          id,
          name,
          sku,
          barcode,
          type,
          price,
          compare_at_price,
          stock_quantity,
          track_inventory,
          avatar,
          images,
          attributes,
          tenant_id,
          created_at,
          updated_at,
          variants:product_variants!product_id(
            id,
            name,
            sku,
            barcode,
            price,
            compare_at_price,
            stock_quantity,
            attributes,
            avatar
          )
        `,
        orderBy: 'created_at',
        ascending: false,
      });

      if (!success || productsError) {
        console.error('Error fetching products:', productsError);
        setTableData([]);
        return;
      }

      // Xử lý dữ liệu để hiển thị trong bảng
      const formattedData = products.map((product) => {
        // Giá trị mặc định cho ngưỡng cảnh báo sắp hết hàng
        const minQuantity = 5;
        const maxQuantity = null;

        // Xử lý biến thể nếu có
        const formattedVariants = product.variants
          ? product.variants.map((variant) => ({
              ...variant,
              price: Number(variant.price || 0),
              compareAtPrice: Number(variant.compareAtPrice || 0),
              stockQuantity: variant.stockQuantity || 0,
              inventory: {
                quantity: variant.stockQuantity || 0,
                minQuantity,
                maxQuantity,
              },
            }))
          : [];

        // Tính toán số lượng tồn kho dựa trên loại sản phẩm
        let quantity = 0;

        if (product.type === 'variable' && formattedVariants.length > 0) {
          // Nếu là sản phẩm biến thể, tính tổng số lượng từ tất cả biến thể
          quantity = formattedVariants.reduce(
            (total, variant) => total + (variant.stockQuantity || 0),
            0
          );
        } else {
          // Nếu là sản phẩm đơn giản, lấy từ trường stock_quantity
          quantity = product.stockQuantity || 0;
        }

        // Xác định trạng thái tồn kho
        let inventoryStatus = 'out_of_stock';
        if (quantity > 0) {
          inventoryStatus = quantity <= minQuantity ? 'low_stock' : 'in_stock';
        }

        // Tạo đối tượng dữ liệu cho mỗi hàng
        return {
          id: product.id,
          productId: product.id,
          name: product.name,
          sku: product.sku,
          type: product.type,
          quantity,
          minQuantity,
          maxQuantity,
          inventoryStatus,
          cover: product.avatar || (product.images && product.images.length > 0 ? product.images[0] : null),
          product: {
            ...product,
            price: Number(product.price || 0),
            compareAtPrice: Number(product.compareAtPrice || 0),
            stockQuantity: product.stockQuantity || 0,
            inventory: {
              quantity,
              minQuantity,
              maxQuantity,
            },
          },
          variants: formattedVariants,
        };
      });

      setTableData(formattedData);
    } catch (error) {
      console.error('Error in fetchProductsInventory:', error);
      setTableData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Tải dữ liệu khi component được mount
  useEffect(() => {
    fetchProductsInventory();
  }, [fetchProductsInventory]);

  const FILTERS = [
    {
      id: 'status',
      label: 'Trạng thái',
      options: [
        { value: 'in_stock', label: 'Còn hàng' },
        { value: 'low_stock', label: 'Sắp hết' },
        { value: 'out_of_stock', label: 'Hết hàng' },
      ],
    },
    {
      id: 'type',
      label: 'Loại sản phẩm',
      options: [
        { value: 'all', label: 'Tất cả' },
        { value: 'simple', label: 'Sản phẩm đơn giản' },
        { value: 'variable', label: 'Sản phẩm biến thể' },
      ],
    },
  ];

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const denseHeight = table.dense ? 60 : 80;

  const canReset = !!filters.name || !!filters.status.length || !!filters.type.length;

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleFilters = useCallback(
    (name, value) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleResetFilters = useCallback(() => {
    setFilters({
      name: '',
      status: [],
      type: [],
    });
  }, []);

  const [currentProduct, setCurrentProduct] = useState(null);

  const handleQuickEdit = useCallback(
    (product, variantId = null) => {
      // Nếu có variantId, đang cập nhật biến thể
      if (variantId) {
        const variant = product.variants.find((v) => v.id === variantId);
        if (variant) {
          setCurrentProduct({
            ...product,
            variantId,
            // Đảm bảo rằng chúng ta vẫn giữ nguyên mảng variants
            // để có thể truy cập nó trong form
          });
        } else {
          setCurrentProduct(product);
        }
      } else {
        // Cập nhật sản phẩm
        setCurrentProduct(product);
      }
      quickEdit.onTrue();
    },
    [quickEdit]
  );

  const handleUpdateInventory = useCallback(
    async (formData) => {
      try {
        if (!currentProduct) return;

        const { quantity, minQuantity = 5 } = formData;
        const { productId, variantId } = currentProduct;
        const now = new Date().toISOString();
        let result;
        let previousQuantity = 0;

        // Lấy số lượng tồn kho hiện tại từ bảng sản phẩm hoặc biến thể
        if (variantId) {
          // Lấy số lượng tồn kho hiện tại của biến thể
          const { success: variantSuccess, data: variantData, error: variantError } = await fetchData(
            'product_variants',
            {
              filters: { id: variantId },
              columns: 'stock_quantity',
              single: true,
            }
          );

          if (!variantSuccess || variantError) {
            toast.error('Không thể lấy thông tin biến thể!');
            return;
          }

          previousQuantity = variantData.stockQuantity || 0;

          // Cập nhật số lượng tồn kho của biến thể
          result = await updateData(
            'product_variants',
            {
              stock_quantity: quantity,
              updated_at: now,
            },
            { id: variantId }
          );
        } else {
          // Lấy số lượng tồn kho hiện tại của sản phẩm
          const { success: productSuccess, data: productData, error: productError } = await fetchData(
            'products',
            {
              filters: { id: productId },
              columns: 'stock_quantity',
              single: true,
            }
          );

          if (!productSuccess || productError) {
            toast.error('Không thể lấy thông tin sản phẩm!');
            return;
          }

          previousQuantity = productData.stockQuantity || 0;

          // Cập nhật số lượng tồn kho của sản phẩm
          result = await updateData(
            'products',
            {
              stock_quantity: quantity,
              updated_at: now,
            },
            { id: productId }
          );
        }

        // Tạo bản ghi giao dịch để lưu lịch sử
        await createData('inventory_transactions', {
          product_id: productId,
          variant_id: variantId,
          type: 'adjustment',
          quantity: quantity - previousQuantity,
          previous_quantity: previousQuantity,
          current_quantity: quantity,
          notes: 'Cập nhật tồn kho trực tiếp',
          created_at: now,
        });

        if (result.success) {
          // Cập nhật dữ liệu bảng
          const updatedData = tableData.map((row) => {
            if (row.id === currentProduct.id) {
              // Xác định trạng thái tồn kho mới
              let inventoryStatus = 'out_of_stock';
              if (quantity > 0) {
                inventoryStatus = quantity <= minQuantity ? 'low_stock' : 'in_stock';
              }

              // Cập nhật dữ liệu hiển thị
              if (variantId) {
                // Cập nhật biến thể
                const updatedVariants = row.variants.map((variant) => {
                  if (variant.id === variantId) {
                    return {
                      ...variant,
                      stockQuantity: quantity,
                      inventory: {
                        ...variant.inventory,
                        quantity,
                        minQuantity,
                      },
                    };
                  }
                  return variant;
                });

                // Tính lại tổng số lượng
                const newTotalQuantity = updatedVariants.reduce(
                  (total, variant) => total + (variant.stockQuantity || 0),
                  0
                );

                return {
                  ...row,
                  quantity: newTotalQuantity,
                  minQuantity,
                  inventoryStatus: newTotalQuantity > 0
                    ? (newTotalQuantity <= minQuantity ? 'low_stock' : 'in_stock')
                    : 'out_of_stock',
                  variants: updatedVariants,
                  product: {
                    ...row.product,
                    stockQuantity: newTotalQuantity,
                    inventory: {
                      ...row.product.inventory,
                      quantity: newTotalQuantity,
                      minQuantity,
                    },
                  },
                };
              } else {
                // Cập nhật sản phẩm
                return {
                  ...row,
                  quantity,
                  minQuantity,
                  inventoryStatus,
                  product: {
                    ...row.product,
                    stockQuantity: quantity,
                    inventory: {
                      ...row.product.inventory,
                      quantity,
                      minQuantity,
                    },
                  },
                };
              }
            }
            return row;
          });

          setTableData(updatedData);
          toast.success('Cập nhật tồn kho thành công!');
          quickEdit.onFalse();

          // Không cần reload lại toàn bộ dữ liệu vì đã cập nhật state
        } else {
          toast.error('Cập nhật tồn kho thất bại!');
        }
      } catch (error) {
        console.error('Error updating inventory:', error);
        toast.error('Đã xảy ra lỗi khi cập nhật tồn kho!');
      }
    },
    [currentProduct, tableData, quickEdit]
  );

  const handleRefresh = useCallback(() => {
    fetchProductsInventory();
  }, [fetchProductsInventory]);

  return (
    <>
      <Card>
        <InventoryTableToolbar
          filters={filters}
          onFilters={handleFilters}
          canReset={canReset}
          onResetFilters={handleResetFilters}
        />

        <InventoryTableFilters
          open={false}
          onClose={() => {}}
          onResetFilters={handleResetFilters}
          filters={filters}
          onFilters={handleFilters}
          filterOptions={FILTERS}
        />

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer
              sx={{
                position: 'relative',
                overflow: 'unset',
                width: '100%',
                '& .MuiTable-root': {
                  tableLayout: 'fixed',
                },
              }}
            >
              <TableSelectedAction
                dense={table.dense}
                numSelected={table.selected.length}
                rowCount={tableData.length}
                onSelectAllRows={(checked) =>
                  table.onSelectAllRows(
                    checked,
                    tableData.map((row) => row.id)
                  )
                }
                action={
                  <Stack direction="row">
                    <Tooltip title="Làm mới">
                      <IconButton color="primary" onClick={handleRefresh}>
                        <Iconify icon="eva:refresh-outline" />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                }
              />

              <Scrollbar>
                <Table
                  size={table.dense ? 'small' : 'medium'}
                  sx={{
                    minWidth: 960,
                    width: '100%',
                    borderCollapse: 'separate',
                    borderSpacing: '0',
                  }}
                >
                  <TableHeadCustom
                    order={table.order}
                    orderBy={table.orderBy}
                    headCells={TABLE_HEAD}
                    rowCount={tableData.length}
                    numSelected={table.selected.length}
                    onSort={table.onSort}
                    onSelectAllRows={(checked) =>
                      table.onSelectAllRows(
                        checked,
                        tableData.map((row) => row.id)
                      )
                    }
                    sx={{
                      '& .MuiTableCell-root': {
                        bgcolor: (theme) => theme.palette.background.neutral,
                        borderBottom: (theme) => `solid 1px ${theme.palette.divider}`,
                      },
                    }}
                  />

                  <TableBody>
                    {dataFiltered
                      .slice(
                        table.page * table.rowsPerPage,
                        table.page * table.rowsPerPage + table.rowsPerPage
                      )
                      .map((row) => (
                        <ProductInventoryTableRow
                          key={row.id}
                          row={row}
                          selected={table.selected.includes(row.id)}
                          onSelectRow={() => table.onSelectRow(row.id)}
                          onQuickEdit={handleQuickEdit}
                        />
                      ))}

                    <TableEmptyRows
                      height={denseHeight}
                      emptyRows={emptyRows(table.page, table.rowsPerPage, tableData.length)}
                    />

                    <TableNoData notFound={notFound} />
                  </TableBody>
                </Table>
              </Scrollbar>
            </TableContainer>

            <TablePaginationCustom
              count={dataFiltered.length}
              page={table.page}
              rowsPerPage={table.rowsPerPage}
              onPageChange={table.onChangePage}
              onRowsPerPageChange={table.onChangeRowsPerPage}
              dense={table.dense}
              onChangeDense={table.onChangeDense}
            />
          </>
        )}
      </Card>

      <InventoryQuickEditForm
        currentProduct={currentProduct}
        open={quickEdit.value}
        onClose={quickEdit.onFalse}
        onSubmit={handleUpdateInventory}
      />
    </>
  );
}

// ----------------------------------------------------------------------

function applyFilter({ inputData, comparator, filters }) {
  const { name, status, type } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index]);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (product) => product.name.toLowerCase().indexOf(name.toLowerCase()) !== -1
    );
  }

  if (status.length) {
    inputData = inputData.filter((product) => status.includes(product.inventoryStatus));
  }

  if (type.length) {
    if (!type.includes('all')) {
      inputData = inputData.filter((product) => type.includes(product.type));
    }
  }

  return inputData;
}

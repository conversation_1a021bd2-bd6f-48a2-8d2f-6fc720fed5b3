'use client';

import PropTypes from 'prop-types';
import { useBoolean } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import Collapse from '@mui/material/Collapse';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import ListItemText from '@mui/material/ListItemText';

import { isVariableProduct } from 'src/actions/mooly-chatbot/product-constants';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

ProductInventoryTableRow.propTypes = {
  onSelectRow: PropTypes.func,
  onQuickEdit: PropTypes.func,
  row: PropTypes.object,
  selected: PropTypes.bool,
};

export default function ProductInventoryTableRow({ row, selected, onSelectRow, onQuickEdit }) {
  const { name, sku, quantity, inventoryStatus, cover, type, variants } = row;

  const showVariants = useBoolean();

  // Xác định màu sắc cho trạng thái tồn kho
  const getInventoryStatusColor = (status) => {
    switch (status) {
      case 'in_stock':
        return 'success';
      case 'low_stock':
        return 'warning';
      case 'out_of_stock':
        return 'error';
      default:
        return 'default';
    }
  };

  // Xác định nhãn cho trạng thái tồn kho
  const getInventoryStatusLabel = (status) => {
    switch (status) {
      case 'in_stock':
        return 'Còn hàng';
      case 'low_stock':
        return 'Sắp hết';
      case 'out_of_stock':
        return 'Hết hàng';
      default:
        return 'Không xác định';
    }
  };

  // Xác định nhãn cho loại sản phẩm
  const getProductTypeLabel = (productType) => {
    switch (productType) {
      case 'simple':
        return 'Đơn giản';
      case 'variable':
        return 'Biến thể';
      default:
        return productType;
    }
  };

  const hasVariants = isVariableProduct({ type }) && variants && variants.length > 0;

  return (
    <>
      <TableRow
        hover
        selected={selected}
        sx={{
          '&:hover': {
            backgroundColor: (theme) => theme.palette.action.hover,
            '& .MuiTableCell-root': {
              borderBottomColor: (theme) => theme.palette.divider,
            },
          },
          '& .MuiTableCell-root': {
            borderBottom: '1px dashed',
            borderBottomColor: (theme) => theme.palette.divider,
            py: 1.5,
          },
        }}
      >
        <TableCell padding="checkbox">
          <Checkbox checked={selected} onClick={onSelectRow} />
        </TableCell>

        <TableCell sx={{ display: 'flex', alignItems: 'center', minWidth: 280 }}>
          <Avatar
            alt={name}
            src={cover}
            variant="rounded"
            sx={{ width: 64, height: 64, mr: 2, borderRadius: 1.5 }}
          />

          <ListItemText
            disableTypography
            primary={
              <Link
                noWrap
                color="inherit"
                variant="subtitle2"
                sx={{ cursor: 'pointer', display: 'block' }}
              >
                {name}
              </Link>
            }
            secondary={
              <Box component="div">
                {sku && (
                  <Typography variant="body2" color="text.disabled" noWrap>
                    SKU: {sku}
                  </Typography>
                )}
              </Box>
            }
          />
        </TableCell>

        <TableCell sx={{ width: 150 }}>
          <Typography variant="body2" color="text.secondary">
            {sku || 'Không có SKU'}
          </Typography>
        </TableCell>

        <TableCell sx={{ width: 150 }}>
          <Label variant="soft" color={isVariableProduct({ type }) ? 'info' : 'success'}>
            {getProductTypeLabel(type)}
          </Label>
        </TableCell>

        <TableCell align="center" sx={{ width: 120 }}>
          <Stack direction="row" alignItems="center" justifyContent="center" spacing={1}>
            {hasVariants ? (
              <Tooltip title="Tổng số lượng từ tất cả biến thể">
                <Stack direction="column" alignItems="center" spacing={0.5}>
                  <Typography variant="body2" fontWeight="medium">
                    {quantity}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ({variants.length} biến thể)
                  </Typography>
                </Stack>
              </Tooltip>
            ) : (
              <Typography variant="body2" fontWeight="medium">
                {quantity}
              </Typography>
            )}
          </Stack>
        </TableCell>

        <TableCell sx={{ width: 120 }}>
          <Label variant="soft" color={getInventoryStatusColor(inventoryStatus)}>
            {getInventoryStatusLabel(inventoryStatus)}
          </Label>
        </TableCell>

        <TableCell align="right" sx={{ width: 120 }}>
          {/* Chỉ hiển thị nút cập nhật tồn kho cho sản phẩm đơn giản */}
          {!hasVariants && (
            <Tooltip title="Cập nhật tồn kho">
              <IconButton color="primary" onClick={() => onQuickEdit(row)}>
                <Iconify icon="eva:edit-fill" />
              </IconButton>
            </Tooltip>
          )}
          {/* Với sản phẩm biến thể, hiển thị icon mở rộng để xem biến thể */}
          {hasVariants && (
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                Xem biến thể
              </Typography>
              <Tooltip title={showVariants.value ? 'Ẩn biến thể' : 'Xem biến thể để cập nhật tồn kho'}>
                <IconButton
                  size="small"
                  color={showVariants.value ? 'primary' : 'default'}
                  onClick={showVariants.onToggle}
                >
                  <Iconify
                    icon={showVariants.value ? 'eva:arrow-up-fill' : 'eva:arrow-down-fill'}
                  />
                </IconButton>
              </Tooltip>
            </Stack>
          )}
        </TableCell>
      </TableRow>

      {hasVariants && (
        <TableRow
          sx={{
            '& .MuiTableCell-root': {
              p: 0,
              borderBottom: '1px solid',
              borderBottomColor: (theme) => theme.palette.divider,
            },
          }}
        >
          <TableCell colSpan={6}>
            <Collapse in={showVariants.value} timeout="auto" unmountOnExit>
              <Box sx={{ py: 2, px: 3 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2,
                  }}
                >
                  <Typography variant="subtitle2" component="div">
                    Biến thể sản phẩm
                  </Typography>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ fontWeight: 'medium' }}
                  >
                    Tổng số lượng tồn kho: <strong>{quantity}</strong> (từ {variants.length} biến
                    thể)
                  </Typography>
                </Box>

                <Box
                  sx={{
                    display: 'grid',
                    gap: 1.5,
                    width: '100%',
                    borderRadius: 1,
                    bgcolor: (theme) => theme.palette.background.neutral,
                    p: 1.5,
                  }}
                >
                  {variants.map((variant) => {
                    // Xác định trạng thái tồn kho của biến thể
                    let variantStatus = 'out_of_stock';
                    const variantQuantity = variant.inventory.quantity;
                    const minQuantity = 5; // Giá trị mặc định cho ngưỡng cảnh báo
                    if (variantQuantity > 0) {
                      variantStatus = variantQuantity <= minQuantity ? 'low_stock' : 'in_stock';
                    }

                    // Xử lý hiển thị thuộc tính biến thể
                    const variantAttributes = variant.attributes
                      ? Object.entries(variant.attributes)
                          .map(([key, value]) => `${key}: ${value}`)
                          .join(', ')
                      : '';

                    // Tính phần trăm đóng góp vào tổng số lượng
                    const percentOfTotal =
                      quantity > 0 ? Math.round((variantQuantity / quantity) * 100) : 0;

                    return (
                      <Stack
                        key={variant.id}
                        direction="row"
                        alignItems="center"
                        spacing={2}
                        sx={{
                          p: 1.5,
                          borderRadius: 1,
                          bgcolor: 'background.paper',
                          boxShadow: (theme) => `0 0 0 1px ${theme.palette.divider}`,
                          '&:hover': {
                            bgcolor: 'background.paper',
                            boxShadow: (theme) => `0 0 0 1px ${theme.palette.primary.main}`,
                          },
                        }}
                      >
                        <Avatar
                          alt={variant.name || ''}
                          src={variant.avatar}
                          variant="rounded"
                          sx={{ width: 48, height: 48, borderRadius: 1 }}
                        />

                        <Typography variant="body2" sx={{ flex: 1, fontWeight: 'medium' }}>
                          {variantAttributes || variant.name || `Biến thể ${variant.id}`}
                          {variant.sku && (
                            <Typography variant="caption" display="block" color="text.secondary">
                              SKU: {variant.sku}
                            </Typography>
                          )}
                        </Typography>

                        <Stack direction="column" alignItems="center" sx={{ width: 80 }}>
                          <Typography
                            variant="body2"
                            sx={{ textAlign: 'center', fontWeight: 'bold' }}
                          >
                            {variant.inventory.quantity}
                          </Typography>
                          {quantity > 0 && (
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                                bgcolor: (theme) => theme.palette.background.neutral,
                                px: 1,
                                py: 0.25,
                                borderRadius: 0.75,
                              }}
                            >
                              <Box
                                sx={{
                                  width: 8,
                                  height: 8,
                                  borderRadius: '50%',
                                  bgcolor: (theme) =>
                                    percentOfTotal > 50
                                      ? theme.palette.success.main
                                      : percentOfTotal > 20
                                        ? theme.palette.warning.main
                                        : theme.palette.error.main,
                                }}
                              />
                              <Typography variant="caption" color="text.secondary">
                                {percentOfTotal}%
                              </Typography>
                            </Box>
                          )}
                        </Stack>

                        <Stack direction="row" spacing={1} alignItems="center">
                          <Label
                            variant="soft"
                            color={getInventoryStatusColor(variantStatus)}
                            sx={{ minWidth: 70, textAlign: 'center' }}
                          >
                            {getInventoryStatusLabel(variantStatus)}
                          </Label>

                          <Tooltip title="Cập nhật tồn kho">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={(e) => {
                                e.stopPropagation();
                                onQuickEdit(row, variant.id);
                              }}
                            >
                              <Iconify icon="eva:edit-fill" width={18} />
                            </IconButton>
                          </Tooltip>
                        </Stack>
                      </Stack>
                    );
                  })}
                </Box>
              </Box>
            </Collapse>
          </TableCell>
        </TableRow>
      )}
    </>
  );
}

'use client';

import { useState } from 'react';

import { snakeToCamelObject } from 'src/utils/format-data/case-converter';

import { supabase } from 'src/lib/supabase';

import { createData } from './supabase-utils';

// Tên bảng trong Supabase
const PRODUCTS_TABLE = 'products';
const PRODUCT_VARIANTS_TABLE = 'product_variants';
const INVENTORY_TRANSACTIONS_TABLE = 'inventory_transactions';

/**
 * L<PERSON>y danh sách tồn kho từ bảng sản phẩm và biến thể
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> quả từ API
 */
export async function getInventoryLevels(options = {}) {
  try {
    // Lấy danh sách sản phẩm với thông tin tồn kho
    const { data: products, error: productsError } = await supabase
      .from(PRODUCTS_TABLE)
      .select(`
        id,
        name,
        sku,
        barcode,
        type,
        price,
        compare_at_price,
        stock_quantity,
        track_inventory,
        avatar,
        images,
        attributes,
        tenant_id,
        created_at,
        updated_at
      `);

    if (productsError) {
      throw productsError;
    }

    // Lấy danh sách biến thể với thông tin tồn kho
    const { data: variants, error: variantsError } = await supabase
      .from(PRODUCT_VARIANTS_TABLE)
      .select(`
        id,
        product_id,
        name,
        sku,
        barcode,
        price,
        compare_at_price,
        stock_quantity,
        attributes,
        avatar,
        tenant_id,
        created_at,
        updated_at
      `);

    if (variantsError) {
      throw variantsError;
    }

    // Tạo danh sách tồn kho từ sản phẩm và biến thể
    const inventoryData = [
      // Tồn kho từ sản phẩm đơn giản (không có biến thể)
      ...products
        .filter(product => product.type !== 'variable')
        .map(product => ({
          id: `product_${product.id}`,
          product_id: product.id,
          variant_id: null,
          quantity: product.stock_quantity || 0,
          min_quantity: 5, // Giá trị mặc định
          max_quantity: null,
          created_at: null,
          updated_at: null,
          tenant_id: product.tenant_id,
          products: product,
          variants: null,
        })),

      // Tồn kho từ biến thể
      ...variants.map(variant => ({
        id: `variant_${variant.id}`,
        product_id: variant.product_id,
        variant_id: variant.id,
        quantity: variant.stock_quantity || 0,
        min_quantity: 5, // Giá trị mặc định
        max_quantity: null,
        created_at: null,
        updated_at: null,
        tenant_id: variant.tenant_id,
        products: products.find(p => p.id === variant.product_id) || null,
        variants: variant,
      })),
    ];

    // Áp dụng các bộ lọc nếu có
    let filteredData = inventoryData;
    if (options.filters) {
      filteredData = inventoryData.filter(item => Object.entries(options.filters).every(([key, value]) => {
          if (value === undefined || value === null) return true;

          // Chuyển đổi key từ camelCase sang snake_case nếu cần
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);

          // Kiểm tra giá trị
          if (key === 'productId' || snakeKey === 'product_id') {
            return item.product_id === value;
          }
          if (key === 'variantId' || snakeKey === 'variant_id') {
            return item.variant_id === value;
          }
          return item[snakeKey] === value;
        }));
    }

    // Chuyển đổi dữ liệu sang camelCase
    const formattedData = snakeToCamelObject(filteredData);

    return { success: true, data: formattedData, error: null };
  } catch (error) {
    console.error('Error in getInventoryLevels:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Lấy danh sách tồn kho với thông tin sản phẩm và biến thể
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getInventoryWithDetails(options = {}) {
  try {
    // Sử dụng hàm getInventoryLevels để lấy dữ liệu
    const result = await getInventoryLevels(options);

    if (!result.success) {
      throw result.error;
    }

    return result;
  } catch (error) {
    console.error('Error in getInventoryWithDetails:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Lấy thông tin tồn kho của một sản phẩm
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProductInventory(productId) {
  try {
    if (!productId) {
      throw new Error('Product ID is required');
    }

    // Lấy thông tin sản phẩm từ bảng products
    const { data, error } = await supabase
      .from(PRODUCTS_TABLE)
      .select('id, name, sku, barcode, type, price, compare_at_price, stock_quantity, images, attributes, tenant_id')
      .eq('id', productId)
      .single();

    if (error) {
      throw error;
    }

    // Tạo đối tượng tồn kho từ dữ liệu sản phẩm
    const inventoryData = {
      id: `product_${data.id}`,
      productId: data.id,
      variantId: null,
      quantity: data.stock_quantity || 0,
      minQuantity: 5, // Giá trị mặc định
      maxQuantity: null,
      product: data,
      variant: null,
    };

    return { success: true, data: [inventoryData], error: null };
  } catch (error) {
    console.error('Error in getProductInventory:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Lấy thông tin tồn kho của một biến thể sản phẩm
 * @param {string} variantId - ID biến thể
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getVariantInventory(variantId) {
  try {
    if (!variantId) {
      throw new Error('Variant ID is required');
    }

    // Lấy thông tin biến thể từ bảng product_variants
    const { data, error } = await supabase
      .from(PRODUCT_VARIANTS_TABLE)
      .select('id, product_id, name, sku, barcode, price, compare_at_price, stock_quantity, attributes, avatar, tenant_id')
      .eq('id', variantId)
      .single();

    if (error) {
      throw error;
    }

    // Lấy thông tin sản phẩm gốc
    const { data: productData, error: productError } = await supabase
      .from(PRODUCTS_TABLE)
      .select('id, name, sku, barcode, type, price, compare_at_price, images, attributes, tenant_id')
      .eq('id', data.product_id)
      .single();

    if (productError) {
      throw productError;
    }

    // Tạo đối tượng tồn kho từ dữ liệu biến thể
    const inventoryData = {
      id: `variant_${data.id}`,
      productId: data.product_id,
      variantId: data.id,
      quantity: data.stock_quantity || 0,
      minQuantity: 5, // Giá trị mặc định
      maxQuantity: null,
      product: productData,
      variant: data,
    };

    return { success: true, data: [inventoryData], error: null };
  } catch (error) {
    console.error('Error in getVariantInventory:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Cập nhật số lượng tồn kho
 * @param {string} productId - ID sản phẩm
 * @param {number} quantity - Số lượng mới
 * @param {string} variantId - ID biến thể (tùy chọn)
 * @param {Object} options - Các tùy chọn bổ sung
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateInventoryQuantity(productId, quantity, variantId = null, options = {}) {
  try {
    if (!productId) {
      throw new Error('Product ID is required');
    }

    if (quantity === undefined || quantity === null) {
      throw new Error('Quantity is required');
    }

    const now = new Date().toISOString();
    let result;
    let previousQuantity = 0;

    if (variantId) {
      // Lấy số lượng tồn kho hiện tại của biến thể
      const { data: variantData, error: variantError } = await supabase
        .from(PRODUCT_VARIANTS_TABLE)
        .select('stock_quantity')
        .eq('id', variantId)
        .single();

      if (variantError) {
        throw variantError;
      }

      previousQuantity = variantData.stock_quantity || 0;

      // Cập nhật số lượng tồn kho của biến thể
      const { data, error } = await supabase
        .from(PRODUCT_VARIANTS_TABLE)
        .update({
          stock_quantity: quantity,
          updated_at: now,
        })
        .eq('id', variantId)
        .select();

      result = { success: !error, data, error };
    } else {
      // Lấy số lượng tồn kho hiện tại của sản phẩm
      const { data: productData, error: productError } = await supabase
        .from(PRODUCTS_TABLE)
        .select('stock_quantity')
        .eq('id', productId)
        .single();

      if (productError) {
        throw productError;
      }

      previousQuantity = productData.stock_quantity || 0;

      // Cập nhật số lượng tồn kho của sản phẩm
      const { data, error } = await supabase
        .from(PRODUCTS_TABLE)
        .update({
          stock_quantity: quantity,
          updated_at: now,
        })
        .eq('id', productId)
        .select();

      result = { success: !error, data, error };
    }

    // Tạo bản ghi giao dịch để lưu lịch sử
    await createInventoryTransaction({
      productId,
      variantId,
      type: 'adjustment',
      quantity: quantity - previousQuantity,
      previousQuantity,
      currentQuantity: quantity,
      notes: options.notes || 'Cập nhật số lượng tồn kho',
      referenceId: options.referenceId,
      referenceType: options.referenceType,
    });

    return result;
  } catch (error) {
    console.error('Error in updateInventoryQuantity:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Tạo giao dịch tồn kho
 * @param {Object} transactionData - Dữ liệu giao dịch
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createInventoryTransaction(transactionData) {
  try {
    const {
      productId,
      variantId,
      type,
      quantity,
      previousQuantity,
      currentQuantity,
      notes,
      referenceId,
      referenceType,
    } = transactionData;

    if (!productId) {
      throw new Error('Product ID is required');
    }

    if (!type) {
      throw new Error('Transaction type is required');
    }

    if (quantity === undefined || quantity === null) {
      throw new Error('Quantity is required');
    }

    const data = {
      product_id: productId,
      variant_id: variantId,
      type,
      quantity,
      previous_quantity: previousQuantity,
      current_quantity: currentQuantity,
      notes,
      reference_id: referenceId,
      reference_type: referenceType,
    };

    return createData(INVENTORY_TRANSACTIONS_TABLE, data);
  } catch (error) {
    console.error('Error in createInventoryTransaction:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lấy lịch sử giao dịch tồn kho
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getInventoryTransactions(options = {}) {
  try {
    // Tạo truy vấn cơ bản
    let query = supabase.from(INVENTORY_TRANSACTIONS_TABLE).select(`
        *,
        products:product_id (*),
        variants:variant_id (*)
      `);

    // Áp dụng các bộ lọc nếu có
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });
    }

    // Sắp xếp theo thời gian tạo, mới nhất lên đầu
    query = query.order('created_at', { ascending: false });

    // Thực hiện truy vấn
    const { data, error } = await query;

    if (error) {
      throw error;
    }

    // Sử dụng hàm chuyển đổi chung từ supabase-utils
    const formattedData = snakeToCamelObject(data).map((item) =>
      // Thêm các trường tính toán hoặc định dạng đặc biệt
      ({
        ...item,
        // Đảm bảo các trường quan hệ được định dạng đúng
        productName: item.products?.name || 'Không xác định',
        variantName: item.variants?.name || null,
        // Đổi tên trường để phù hợp với UI
        date: item.createdAt,
        userName: 'Hệ thống' // Mặc định là hệ thống vì không có quan hệ với users
      })
    );

    return { success: true, data: formattedData, error: null };
  } catch (error) {
    console.error('Error in getInventoryTransactions:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Điều chỉnh tồn kho (tăng hoặc giảm)
 * @param {string} productId - ID sản phẩm
 * @param {number} adjustmentQuantity - Số lượng điều chỉnh (dương để tăng, âm để giảm)
 * @param {string} variantId - ID biến thể (tùy chọn)
 * @param {Object} options - Các tùy chọn bổ sung
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function adjustInventory(
  productId,
  adjustmentQuantity,
  variantId = null,
  options = {}
) {
  try {
    if (!productId) {
      throw new Error('Product ID is required');
    }

    if (adjustmentQuantity === undefined || adjustmentQuantity === null) {
      throw new Error('Adjustment quantity is required');
    }

    const now = new Date().toISOString();
    let result;
    let previousQuantity = 0;
    let newQuantity = 0;

    if (variantId) {
      // Lấy số lượng tồn kho hiện tại của biến thể
      const { data: variantData, error: variantError } = await supabase
        .from(PRODUCT_VARIANTS_TABLE)
        .select('stock_quantity')
        .eq('id', variantId)
        .single();

      if (variantError) {
        throw variantError;
      }

      previousQuantity = variantData.stock_quantity || 0;
      newQuantity = previousQuantity + adjustmentQuantity;

      // Đảm bảo số lượng không âm
      if (newQuantity < 0 && !options.allowNegative) {
        throw new Error('Insufficient inventory. Cannot reduce below zero.');
      }

      // Cập nhật số lượng tồn kho của biến thể
      const { data, error } = await supabase
        .from(PRODUCT_VARIANTS_TABLE)
        .update({
          stock_quantity: newQuantity,
          updated_at: now,
        })
        .eq('id', variantId)
        .select();

      result = { success: !error, data, error };
    } else {
      // Lấy số lượng tồn kho hiện tại của sản phẩm
      const { data: productData, error: productError } = await supabase
        .from(PRODUCTS_TABLE)
        .select('stock_quantity')
        .eq('id', productId)
        .single();

      if (productError) {
        throw productError;
      }

      previousQuantity = productData.stock_quantity || 0;
      newQuantity = previousQuantity + adjustmentQuantity;

      // Đảm bảo số lượng không âm
      if (newQuantity < 0 && !options.allowNegative) {
        throw new Error('Insufficient inventory. Cannot reduce below zero.');
      }

      // Cập nhật số lượng tồn kho của sản phẩm
      const { data, error } = await supabase
        .from(PRODUCTS_TABLE)
        .update({
          stock_quantity: newQuantity,
          updated_at: now,
        })
        .eq('id', productId)
        .select();

      result = { success: !error, data, error };
    }

    // Xác định loại giao dịch
    const transactionType =
      adjustmentQuantity > 0
        ? options.transactionType || 'stock_in'
        : options.transactionType || 'stock_out';

    // Tạo bản ghi giao dịch
    await createInventoryTransaction({
      productId,
      variantId,
      type: transactionType,
      quantity: adjustmentQuantity,
      previousQuantity,
      currentQuantity: newQuantity,
      notes:
        options.notes ||
        `Điều chỉnh tồn kho: ${adjustmentQuantity > 0 ? 'Tăng' : 'Giảm'} ${Math.abs(adjustmentQuantity)}`,
      referenceId: options.referenceId,
      referenceType: options.referenceType,
    });

    return result;
  } catch (error) {
    console.error('Error in adjustInventory:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lấy danh sách sản phẩm sắp hết hàng
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getLowStockProducts(options = {}) {
  try {
    // Lấy danh sách sản phẩm với thông tin tồn kho
    const { data: products, error: productsError } = await supabase
      .from(PRODUCTS_TABLE)
      .select('id, name, sku, barcode, type, price, compare_at_price, stock_quantity, images, attributes, tenant_id');

    if (productsError) {
      throw productsError;
    }

    // Lấy danh sách biến thể với thông tin tồn kho
    const { data: variants, error: variantsError } = await supabase
      .from(PRODUCT_VARIANTS_TABLE)
      .select('id, product_id, name, sku, barcode, price, compare_at_price, stock_quantity, attributes, avatar, tenant_id');

    if (variantsError) {
      throw variantsError;
    }

    // Ngưỡng tồn kho tối thiểu mặc định
    const DEFAULT_MIN_QUANTITY = 5;

    // Lọc sản phẩm sắp hết hàng
    const lowStockProducts = products
      .filter(product => product.type !== 'variable') // Chỉ lấy sản phẩm đơn giản
      .filter(product => {
        const stockQuantity = product.stock_quantity || 0;
        return stockQuantity > 0 && stockQuantity <= DEFAULT_MIN_QUANTITY;
      })
      .map(product => ({
        id: `product_${product.id}`,
        productId: product.id,
        variantId: null,
        quantity: product.stock_quantity || 0,
        minQuantity: DEFAULT_MIN_QUANTITY,
        maxQuantity: null,
        product,
        variant: null,
      }));

    // Lọc biến thể sắp hết hàng
    const lowStockVariants = variants
      .filter(variant => {
        const stockQuantity = variant.stock_quantity || 0;
        return stockQuantity > 0 && stockQuantity <= DEFAULT_MIN_QUANTITY;
      })
      .map(variant => {
        const product = products.find(p => p.id === variant.product_id);
        return {
          id: `variant_${variant.id}`,
          productId: variant.product_id,
          variantId: variant.id,
          quantity: variant.stock_quantity || 0,
          minQuantity: DEFAULT_MIN_QUANTITY,
          maxQuantity: null,
          product: product || null,
          variant,
        };
      });

    // Kết hợp kết quả
    const combinedResults = [...lowStockProducts, ...lowStockVariants];

    // Áp dụng các bộ lọc bổ sung nếu có
    let resultData = combinedResults;
    if (options.filters) {
      resultData = combinedResults.filter(item =>
        Object.entries(options.filters).every(([key, value]) => {
          if (value === undefined || value === null) return true;

          // Chuyển đổi key từ camelCase sang snake_case nếu cần
          const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);

          // Kiểm tra giá trị
          if (key === 'productId' || snakeKey === 'product_id') {
            return item.productId === value;
          }
          if (key === 'variantId' || snakeKey === 'variant_id') {
            return item.variantId === value;
          }
          return item[key] === value || item[snakeKey] === value;
        })
      );
    }

    // Chuyển đổi dữ liệu sang camelCase nếu cần
    const formattedData = snakeToCamelObject(resultData);

    return { success: true, data: formattedData, error: null };
  } catch (error) {
    console.error('Error in getLowStockProducts:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Lấy danh sách sản phẩm kèm thông tin tồn kho - Tối ưu với một truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProductsWithInventory() {
  try {
    // Lấy danh sách sản phẩm với biến thể trong một truy vấn
    const { data: products, error: productsError } = await supabase
      .from(PRODUCTS_TABLE)
      .select(`
        id,
        name,
        sku,
        barcode,
        type,
        price,
        compare_at_price,
        stock_quantity,
        track_inventory,
        avatar,
        images,
        attributes,
        tenant_id,
        created_at,
        updated_at,
        variants:product_variants!product_id(
          id,
          name,
          sku,
          barcode,
          price,
          compare_at_price,
          stock_quantity,
          attributes,
          avatar
        )
      `)
      .order('created_at', { ascending: false });

    if (productsError) {
      throw productsError;
    }

    // Xử lý dữ liệu sản phẩm
    const productsWithInventory = products.map((product) => {
      // Lấy thông tin tồn kho trực tiếp từ sản phẩm
      const stockQuantity = product.stock_quantity || 0;

      // Xử lý biến thể nếu có
      const variants = product.variants ? product.variants.map((variant) => ({
        ...variant,
        inventory: {
          quantity: variant.stock_quantity || 0,
          minQuantity: 5, // Giá trị mặc định
          maxQuantity: null,
        },
      })) : [];

      return {
        ...product,
        inventory: {
          quantity: stockQuantity,
          minQuantity: 5, // Giá trị mặc định
          maxQuantity: null,
        },
        variants,
      };
    });

    // Chuyển đổi dữ liệu sang camelCase
    const formattedData = snakeToCamelObject(productsWithInventory);

    return { success: true, data: formattedData, error: null };
  } catch (error) {
    console.error('Error in getProductsWithInventory:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Lấy tồn kho của tất cả sản phẩm và biến thể
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getAllProductsInventory() {
  try {
    // Lấy danh sách sản phẩm với thông tin tồn kho
    const { data: products, error: productsError } = await supabase
      .from(PRODUCTS_TABLE)
      .select(`
        id,
        name,
        sku,
        barcode,
        type,
        price,
        compare_at_price,
        stock_quantity,
        track_inventory,
        avatar,
        images,
        attributes,
        tenant_id,
        created_at,
        updated_at
      `);

    if (productsError) {
      throw productsError;
    }

    // Lấy danh sách biến thể với thông tin tồn kho
    const { data: variants, error: variantsError } = await supabase
      .from(PRODUCT_VARIANTS_TABLE)
      .select(`
        id,
        product_id,
        name,
        sku,
        barcode,
        price,
        compare_at_price,
        stock_quantity,
        attributes,
        avatar,
        tenant_id,
        created_at,
        updated_at
      `);

    if (variantsError) {
      throw variantsError;
    }

    // Tạo danh sách tồn kho từ sản phẩm và biến thể
    const inventoryData = [
      // Tồn kho từ sản phẩm
      ...products.map(product => ({
        id: `product_${product.id}`,
        productId: product.id,
        variantId: null,
        quantity: product.stock_quantity || 0,
        minQuantity: 5, // Giá trị mặc định
        maxQuantity: null,
        product,
        variant: null,
      })),

      // Tồn kho từ biến thể
      ...variants.map(variant => {
        const product = products.find(p => p.id === variant.product_id);
        return {
          id: `variant_${variant.id}`,
          productId: variant.product_id,
          variantId: variant.id,
          quantity: variant.stock_quantity || 0,
          minQuantity: 5, // Giá trị mặc định
          maxQuantity: null,
          product: product || null,
          variant,
        };
      }),
    ];

    // Chuyển đổi dữ liệu sang camelCase
    const formattedData = snakeToCamelObject(inventoryData);

    return { success: true, data: formattedData, error: null };
  } catch (error) {
    console.error('Error in getAllProductsInventory:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Lấy tồn kho của sản phẩm theo loại
 * @param {string} productType - Loại sản phẩm ('simple', 'variable', etc.)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getInventoryByProductType(productType) {
  try {
    // Lấy tất cả dữ liệu tồn kho
    const inventoryResult = await getAllProductsInventory();

    if (!inventoryResult.success) {
      throw new Error('Failed to fetch inventory data');
    }

    // Lọc theo loại sản phẩm
    const filteredData = inventoryResult.data.filter(
      item => item.product && item.product.type === productType
    );

    return { success: true, data: filteredData, error: null };
  } catch (error) {
    console.error(`Error in getInventoryByProductType for type ${productType}:`, error);
    return { success: false, error, data: [] };
  }
}

/**
 * Hook để quản lý tồn kho
 * @returns {Object} - Các hàm và trạng thái liên quan đến tồn kho
 */
export function useInventory() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Hàm wrapper để xử lý loading và error
  const handleAsync = async (asyncFn, ...args) => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await asyncFn(...args);
      if (!result.success) {
        setError(result.error);
      }
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  // Các hàm được wrap với xử lý loading và error
  const getInventory = (...args) => handleAsync(getInventoryWithDetails, ...args);
  const getProductStock = (...args) => handleAsync(getProductInventory, ...args);
  const getVariantStock = (...args) => handleAsync(getVariantInventory, ...args);
  const updateStock = (...args) => handleAsync(updateInventoryQuantity, ...args);
  const adjustStock = (...args) => handleAsync(adjustInventory, ...args);
  const getTransactions = (...args) => handleAsync(getInventoryTransactions, ...args);
  const getLowStock = (...args) => handleAsync(getLowStockProducts, ...args);
  const getAllInventory = () => handleAsync(getAllProductsInventory);
  const getInventoryByType = (type) => handleAsync(getInventoryByProductType, type);
  const getProductsInventory = () => handleAsync(getProductsWithInventory);

  return {
    isLoading,
    error,
    getInventory,
    getProductStock,
    getVariantStock,
    updateStock,
    adjustStock,
    getTransactions,
    getLowStock,
    getAllInventory,
    getInventoryByType,
    getProductsInventory,
  };
}
